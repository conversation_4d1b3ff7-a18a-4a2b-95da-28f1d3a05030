import React from 'react'
import {useRef , useEffect} from 'react'

import {gsap} from 'gsap'
import { useGSAP } from '@gsap/react'
const CustomCursor = () => {

    // references for cursor elements
    const cursorRef = useRef(null);
    const cursorBorderREf = useRef(null);

    // hide on mobiles and tablets

    const isMobile = typeof window != "undefined" &&
    window.matchMedia("(max-width:768px)").matches

    if(isMobile){
        return null;
    }

    useEffect(()=>{
        const cursor = cursorRef.current;
        const cursorBorder = cursorBorderREf.current;

        
            gsap.set([cursor , cursorBorder],{
                x:"-50%",
                y:"-50%"
            })

            const xTo = gsap.quickTo(cursor , "x" ,{
                duration:0.2 , ease:"power3.out"
            })
            const xBorderTo = gsap.quickTo(cursor , "x" ,{
                duration:0.5 , ease:"power.out"
            })
            const yTo = gsap.quickTo(cursor , "y" , {
                duration:0.2 , ease:"power3.out"
            })
            const yBorderTo = gsap.quickTo(cursor , "y" , {
                duration:0.5 , ease:"power3.out"
            })

            const handleMove = (e) =>{
                xTo(e.clientX)
                yTo(e.clientY)
                xBorderTo(e.clientX)
                yBorderTo(e.clientY)
            }

            window.addEventListener("mousemove" , handleMove)

    },[])
  return (
    <>
        <div ref={cursorRef} className="fixed top-0 left-0 w-[20px] h-[20px] bg-text rounded-full pointer-events-none z-[999] mix-blend-difference"></div>
        <div ref = {cursorBorderREf} className="fixed top-0 left-0 w-[40px] h-[40px] border rounded-full border-text pointer-events-none z-[999] mix-blend-difference opacity-50"></div>
    </>
  )
}

export default CustomCursor