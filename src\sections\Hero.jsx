import React, { useRef } from 'react'
import Header from '../components/Header'
import { gsap } from 'gsap'
import { useGSAP } from '@gsap/react'
import { FiDownload, FiArrowDown, FiGithub, FiLinkedin, FiMail } from 'react-icons/fi'

const Hero = () => {
  // Refs for GSAP animations
  const heroRef = useRef(null)
  const titleRef = useRef(null)
  const subtitleRef = useRef(null)
  const descriptionRef = useRef(null)
  const buttonsRef = useRef(null)
  const socialRef = useRef(null)
  const scrollIndicatorRef = useRef(null)
  const backgroundRef = useRef(null)

  // Hero animations
  useGSAP(() => {
    const tl = gsap.timeline({ delay: 1.8 }) // Start after header animations

    // Background gradient animation
    tl.fromTo(backgroundRef.current,
      { opacity: 0, scale: 0.8 },
      {
        opacity: 1,
        scale: 1,
        duration: 2,
        ease: "power2.out"
      }
    )

    // Main title animation
    .fromTo(titleRef.current,
      { opacity: 0, y: 100, rotationX: -90 },
      {
        opacity: 1,
        y: 0,
        rotationX: 0,
        duration: 1.2,
        ease: "back.out(1.7)"
      }, "-=1.5"
    )

    // Subtitle animation
    .fromTo(subtitleRef.current,
      { opacity: 0, x: -100 },
      {
        opacity: 1,
        x: 0,
        duration: 1,
        ease: "power3.out"
      }, "-=0.8"
    )

    // Description animation
    .fromTo(descriptionRef.current,
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out"
      }, "-=0.5"
    )

    // Buttons animation
    .fromTo(buttonsRef.current.children,
      { opacity: 0, y: 30, scale: 0.8 },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.6,
        stagger: 0.2,
        ease: "back.out(1.7)"
      }, "-=0.3"
    )

    // Social links animation
    .fromTo(socialRef.current.children,
      { opacity: 0, scale: 0, rotation: 180 },
      {
        opacity: 1,
        scale: 1,
        rotation: 0,
        duration: 0.5,
        stagger: 0.1,
        ease: "back.out(1.7)"
      }, "-=0.4"
    )

    // Scroll indicator animation
    .fromTo(scrollIndicatorRef.current,
      { opacity: 0, y: -20 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out"
      }, "-=0.2"
    )

    // Floating animation for scroll indicator
    .to(scrollIndicatorRef.current,
      {
        y: 10,
        duration: 1.5,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut"
      }
    )

  }, { scope: heroRef })

  return (
    <>
      <Header />

      <section
        ref={heroRef}
        className="relative min-h-screen flex items-center justify-center overflow-hidden bg-background"
      >
        {/* Animated Background Elements */}
        <div
          ref={backgroundRef}
          className="absolute inset-0 opacity-20"
        >
          {/* Gradient Orbs */}
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-primary/30 to-accent/30 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-accent/20 to-secondary/30 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-secondary/20 to-primary/20 rounded-full blur-2xl"></div>
        </div>

        {/* Main Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">

          {/* Subtitle */}
          <div
            ref={subtitleRef}
            className="mb-6"
          >
            <span className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-primary/10 to-accent/10 border border-primary/20 text-primary text-sm font-medium">
              👋 Welcome to my portfolio
            </span>
          </div>

          {/* Main Title */}
          <h1
            ref={titleRef}
            className="text-4xl sm:text-6xl lg:text-7xl xl:text-8xl font-bold mb-6 leading-tight"
          >
            <span className="block bg-gradient-to-r from-text via-primary to-accent bg-clip-text text-transparent">
              Hi, I'm
            </span>
            <span className="block bg-gradient-to-r from-accent via-primary to-text bg-clip-text text-transparent mt-2">
              Harshith
            </span>
          </h1>

          {/* Description */}
          <p
            ref={descriptionRef}
            className="text-lg sm:text-xl lg:text-2xl text-text/80 mb-12 max-w-3xl mx-auto leading-relaxed"
          >
            A passionate <span className="text-accent font-semibold">Full Stack Developer</span> crafting
            beautiful and functional web experiences with modern technologies.
          </p>

          {/* Action Buttons */}
          <div
            ref={buttonsRef}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
          >
            <button className="group relative px-8 py-4 bg-gradient-to-r from-accent to-primary text-white font-semibold rounded-xl hover:shadow-2xl hover:shadow-accent/25 transition-all duration-300 transform hover:scale-105 flex items-center gap-3">
              <FiDownload className="text-xl group-hover:animate-bounce" />
              Download Resume
              <div className="absolute inset-0 bg-gradient-to-r from-accent to-primary rounded-xl blur opacity-0 group-hover:opacity-50 transition-opacity duration-300 -z-10"></div>
            </button>

            <button className="group px-8 py-4 border-2 border-primary/50 text-primary font-semibold rounded-xl hover:bg-primary/10 hover:border-primary transition-all duration-300 transform hover:scale-105 flex items-center gap-3">
              <FiMail className="text-xl group-hover:rotate-12 transition-transform duration-300" />
              Get In Touch
            </button>
          </div>

          {/* Social Links */}
          <div
            ref={socialRef}
            className="flex justify-center gap-6 mb-16"
          >
            <a
              href="#"
              className="group p-4 rounded-full bg-gradient-to-r from-primary/10 to-accent/10 border border-primary/20 hover:border-accent/50 transition-all duration-300 hover:scale-110"
            >
              <FiGithub className="text-2xl text-text group-hover:text-accent transition-colors duration-300" />
            </a>
            <a
              href="#"
              className="group p-4 rounded-full bg-gradient-to-r from-primary/10 to-accent/10 border border-primary/20 hover:border-accent/50 transition-all duration-300 hover:scale-110"
            >
              <FiLinkedin className="text-2xl text-text group-hover:text-accent transition-colors duration-300" />
            </a>
            <a
              href="#"
              className="group p-4 rounded-full bg-gradient-to-r from-primary/10 to-accent/10 border border-primary/20 hover:border-accent/50 transition-all duration-300 hover:scale-110"
            >
              <FiMail className="text-2xl text-text group-hover:text-accent transition-colors duration-300" />
            </a>
          </div>

        </div>

        {/* Scroll Indicator */}
        <div
          ref={scrollIndicatorRef}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center gap-2 text-text/60"
        >
          <span className="text-sm font-medium">Scroll Down</span>
          <div className="p-2 rounded-full border border-text/20">
            <FiArrowDown className="text-xl animate-bounce" />
          </div>
        </div>

      </section>
    </>
  )
}

export default Hero