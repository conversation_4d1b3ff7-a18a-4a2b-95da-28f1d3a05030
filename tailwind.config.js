/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'text': 'hsl(212, 42%, 91%)',
        'background': 'hsl(213, 52%, 4%)',
        'primary': 'hsl(213, 69%, 71%)',
        'secondary': 'hsl(212, 83%, 32%)',
        'accent': 'hsl(213, 96%, 59%)',
      },
      backdropBlur: {
        'xs': '2px',
      },
    },
  },
  plugins: [
    function({ addUtilities }) {
      const newUtilities = {
        '.glass-card': {
          'background': 'hsla(213, 69%, 71%, 0.1)',
          'backdrop-filter': 'blur(10px)',
          '-webkit-backdrop-filter': 'blur(10px)',
          'border': '1px solid hsla(213, 69%, 71%, 0.2)',
          'box-shadow': '0 8px 32px 0 hsla(213, 52%, 4%, 0.37)',
        },
        '.glass-card-subtle': {
          'background': 'hsla(212, 42%, 91%, 0.05)',
          'backdrop-filter': 'blur(8px)',
          '-webkit-backdrop-filter': 'blur(8px)',
          'border': '1px solid hsla(212, 42%, 91%, 0.1)',
          'box-shadow': '0 4px 16px 0 hsla(213, 52%, 4%, 0.25)',
        },
        '.glass-card-accent': {
          'background': 'hsla(213, 96%, 59%, 0.08)',
          'backdrop-filter': 'blur(12px)',
          '-webkit-backdrop-filter': 'blur(12px)',
          'border': '1px solid hsla(213, 96%, 59%, 0.15)',
          'box-shadow': '0 8px 32px 0 hsla(213, 96%, 59%, 0.1)',
        },
        '.glass-nav': {
          'background': 'hsla(213, 52%, 4%, 0.8)',
          'backdrop-filter': 'blur(16px)',
          '-webkit-backdrop-filter': 'blur(16px)',
          'border-bottom': '1px solid hsla(213, 69%, 71%, 0.15)',
        },
      }
      
      addUtilities(newUtilities, ['responsive', 'hover'])
    }
  ],
}
