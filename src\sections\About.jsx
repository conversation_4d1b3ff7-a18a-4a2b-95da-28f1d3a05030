import React from 'react'
import {useRef , useEffect} from 'react'

import {gsap} from 'gsap'
import { ScrollTrigger } from 'gsap/all'
const About = () => {
  const sectionRef = useRef(null)
  const titleRef = useRef(null)
  useEffect(()=>{
    gsap.registerPlugin(ScrollTrigger)
    gsap.fromTo(
      titleRef.current,
      {y:100 , opacity:0},
      {y:-300,opacity:1,duration:0.8,
        scrollTrigger:{
          trigger:sectionRef.current,
          start:"top 40%",
          toggleActions:"play none none reverse"
        }
      }
    )
  })

  return (
    <section ref = {sectionRef} className='h-screen relative overflow-hidden bg-gradient-to-t from-black to-background'> 

    <div className='container mx-auto flex flex-col items-center justify-center h-full'>
      <h1 ref = {titleRef} className='text-4xl md:text-6xl font-bold text-text'>Hello World</h1>
    </div>
    <div className='absolute lg:bottom-[-20rem] md:bottom-[-20rem] bottom[-20rem] left-0 w-full flex md:flex-row flex-col justify-between lg:px-24 px-5 items-center opactiy-0 '>

      <h3 className='text-md md:text-2xl font-bold text-text'>
      Lorem, ipsum dolor sit amet consectetur adipisicing elit. Excepturi, corporis corrupti quis distinctio accusantium officiis voluptas in sed laborum laboriosam? Eum et quisquam maxime numquam tempora distinctio molestiae vel non.
      </h3>
    </div>

    </section>
  )
}

export default About