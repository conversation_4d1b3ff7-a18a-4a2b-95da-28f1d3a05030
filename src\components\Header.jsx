import React from 'react'
import { navlinks } from '../consts/consts'
import { FiGithub , FiX ,FiLinkedin ,FiMenu } from 'react-icons/fi'
import {useState, useRef} from 'react'
import { gsap } from 'gsap'
import { useGSAP } from '@gsap/react'

const Header = () => {

  const [isOpen, setIsOpen] = useState(false)

  // Refs for GSAP animations
  const containerRef = useRef(null)
  const logoRef = useRef(null)
  const navLinksRef = useRef([])
  const mobileLinksRef = useRef([])

  // Initialize animations on component mount
  useGSAP(() => {
    const tl = gsap.timeline()

    // Logo animation
    tl.fromTo(logoRef.current,
      { opacity: 0, y: -100 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "back.out(1.7)",
        delay: 0.5
      }
    )

    // Navigation links staggered animation
    .fromTo(navLinksRef.current,
      { opacity: 0, y: -100 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "back.out(1.7)",
        stagger: 0.2,
        delay: 0.7
      }, 
    )


  }, { scope: containerRef })

  // Mobile menu animation
  useGSAP(() => {
    if (isOpen && mobileLinksRef.current.length > 0) {
      gsap.fromTo(mobileLinksRef.current,
        { opacity: 0, x: 100 },
        {
          opacity: 1,
          x: 0,
          duration: 0.8,
          ease: "back.out(1.7)",
          stagger: 0.3
        }
      )
    }
  }, { dependencies: [isOpen], scope: containerRef })
  return (
    <>
      <header ref={containerRef} className="absolute w-full z-50 transition-all duration-300 mt-5">
        <div className="w-full px-4 sm:px-6 lg:px-12 xl:px-16 flex justify-between items-center h-16 md:h-20">
          {/* Logo */}
          <div ref={logoRef} className="flex items-center">
            <img src="src\assets\Images\header.png" alt="Header Image"/>
          </div>
          
          <nav className="lg:flex lg:gap-8 md:flex md:gap-8 sm:hidden lg:space-x-12 ">
            {navlinks.map(({name,href}, index) => (
              <a
                key={name}
                href={href}
                ref={el => navLinksRef.current[index] = el}
                className="relative bg-gradient-to-r from-gray-500 to-gray-100 bg-clip-text text-transparent hover:text-accent  duration-300 text-2xl transition-colors group"
              >
                {name}
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-accent group-hover:w-full transition-all duration-300"></span>
              </a>
            ))}
          </nav>
          {/* Mobile Menu */}
          <div className="md:hidden flex items-center">
            <div className="lg:hidden flex items-center">
              <button 
                onClick={() => setIsOpen(!isOpen)}
                className="text-text hover:text-accent transition-colors duration-300"
              >
                {isOpen ? (
                  <FiX className="text-2xl" />
                ) : (
                  <FiMenu className="text-2xl" />
                )}
              </button>
            </div>

            {/* Mobile Menu Panel */}
            {isOpen && (
              <>
                <button 
                  onClick={() => setIsOpen(false)}
                  className="fixed top-4 right-4 text-text hover:text-accent transition-colors duration-300 z-[70]"
                >
                  <FiX className="text-2xl" />
                </button>
              <div className="lg:hidden fixed inset-0 bg-background/95 z-[60] flex items-center justify-center">
                <nav className="flex flex-col items-center gap-8">
                  {navlinks.map(({name, href}, index) => (
                    <a
                      key={name}
                      href={href}
                      ref={el => mobileLinksRef.current[index] = el}
                      onClick={() => setIsOpen(false)}
                      className="text-2xl text-text hover:text-accent transition-colors duration-300"
                    >
                      {name}
                    </a>
                  ))}
                </nav>
              </div>
              </>
            )}
          </div>
        </div>
      </header>
    </>
  )
}

export default Header