import React from 'react'
// eslint-disable-next-line no-unused-vars
import {motion} from 'framer-motion'
import { navlinks } from '../consts/consts'
import { FiGithub , FiX ,FiLinkedin ,FiMenu } from 'react-icons/fi'
import {useState} from 'react'

const Header = () => {
  
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
      <header className="absolute w-full z-50 transition-all duration-300">
        <div className="w-full px-4 sm:px-6 lg:px-12 xl:px-16 flex justify-between items-center h-16 md:h-20">
          {/* Logo */}
          <motion.div
          initial={{opacity:0, y:-100}}
          animate={{opacity:1, y:0}}
          transition={{
            "type":"spring",
            "duration":1,
            "stiffness":200,
            "delay":0.5
          }}
           className="flex items-center">
            <img src="src\assets\Images\header.png" alt="Header Image" srcset="" />
          </motion.div>
          
          <nav className="lg:flex lg:gap-8 md:flex md:gap-8 sm:hidden lg:space-x-12 ">
            {navlinks.map(({name,href,id}) => (
              <motion.a
              initial={{opacity:0, y:-100}}
              animate={{opacity:1, y:0}}
              transition={{
                "type":"spring",
                "duration":1,
                "stiffness":200,
                "damping":20,
                "delay":0.7+id*0.2,
              }}
               key={name} href={href} className="relative bg-gradient-to-r from-gray-500 to-gray-100 bg-clip-text text-transparent hover:text-accent  duration-300 text-2xl transition-colors group">
                {name}
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-accent group-hover:w-full transition-all duration-300"></span>
              </motion.a>
            ))}
          </nav>


          <div className="md:flex hidden items-center space-x-4">
            <motion.a 
            initial={{opacity:0, scale:0.5}}
            animate={{opacity:1, scale:1}}
            transition={{
              "duration":1,
              "delay":1.5
            }}
            href="" className='hover:text-accent text-text transition-colors duration-300'>
              <FiGithub className="text-2xl"/>
            </motion.a>

            <motion.a 
            initial={{opacity:0, scale:0.5}}
            animate={{opacity:1, scale:1}}
            transition={{
              "duration":1,
              "delay":1.5
            }}
            href="" className='hover:text-accent text-text transition-colors duration-300'>
              <FiLinkedin className="text-2xl"/>
            </motion.a>
          </div>
          {/* Mobile Menu */}
          <div className="md:hidden flex items-center">
            <div className="lg:hidden flex items-center">
              <button 
                onClick={() => setIsOpen(!isOpen)}
                className="text-text hover:text-accent transition-colors duration-300"
              >
                {isOpen ? (
                  <FiX className="text-2xl" />
                ) : (
                  <FiMenu className="text-2xl" />
                )}
              </button>
            </div>

            {/* Mobile Menu Panel */}
            {isOpen && (
              <>
                <button 
                  onClick={() => setIsOpen(false)}
                  className="fixed top-4 right-4 text-text hover:text-accent transition-colors duration-300 z-[70]"
                >
                  <FiX className="text-2xl" />
                </button>
              <div className="lg:hidden fixed inset-0 bg-background/95 z-[60] flex items-center justify-center">
                <nav className="flex flex-col items-center gap-8">
                  {navlinks.map(({name, href, id}) => (
                    <motion.a
                      key={name}
                      href={href}
                      onClick={() => setIsOpen(false)}
                      className="text-2xl text-text hover:text-accent transition-colors duration-300"
                      initial={{ opacity: 0, x:100 }}
                      animate={{ opacity: 1, x: 0 }}
                      type='spring'
                      damping={20}
                      stiffness={100}
                      transition={{ delay: id * 0.3 }}
                      duration={1}
                    >
                      {name}
                    </motion.a>
                  ))}
                </nav>
              </div>
              </>
            )}
          </div>
        </div>
      </header>
    </>
  )
}

export default Header